#!/bin/bash

# Comprehensive Billing Disable Script for Suna Development
# This script transforms localhost into a production-like experience with all premium features enabled
# It leverages the existing ENV_MODE=local infrastructure built into the system

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Helper functions for colored output
info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
success() { echo -e "${GREEN}✅ $1${NC}"; }
warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
error() { echo -e "${RED}❌ $1${NC}"; }
section() { echo -e "\n${PURPLE}🔧 $1${NC}"; }
feature() { echo -e "${CYAN}🚀 $1${NC}"; }

BACKUP_DIR="complete_billing_disable_backup_$(date +%Y%m%d_%H%M%S)"

echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${PURPLE}║                                                              ║${NC}"
echo -e "${PURPLE}║          🚀 SUNA COMPREHENSIVE BILLING DISABLE 🚀          ║${NC}"
echo -e "${PURPLE}║                                                              ║${NC}"
echo -e "${PURPLE}║  Transform localhost into production-like experience        ║${NC}"
echo -e "${PURPLE}║  • Unlimited usage & all premium features                   ║${NC}"
echo -e "${PURPLE}║  • All models accessible (GPT-4, Claude, etc.)              ║${NC}"
echo -e "${PURPLE}║  • Custom agents & advanced workflows enabled              ║${NC}"
echo -e "${PURPLE}║  • Complete billing bypass with rollback support          ║${NC}"
echo -e "${PURPLE}║                                                              ║${NC}"
echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""

info "📦 Creating comprehensive backup directory: $BACKUP_DIR"
mkdir -p "$BACKUP_DIR"

# =============================================================================
# STEP 1: BACKEND CONFIGURATION - Activate Local Development Mode
# =============================================================================

section "Backend Configuration - Activating Local Development Mode"

BACKEND_ENV="backend/.env"
if [ -f "$BACKEND_ENV" ]; then
    cp "$BACKEND_ENV" "$BACKUP_DIR/.env.backend.backup"
    success "Backed up backend .env"
    
    # Set ENV_MODE to local (this is the master switch that disables billing throughout the system)
    if grep -q "^ENV_MODE=" "$BACKEND_ENV"; then
        sed -i.bak 's/^ENV_MODE=.*/ENV_MODE=local/' "$BACKEND_ENV"
    else
        echo "ENV_MODE=local" >> "$BACKEND_ENV"
    fi
    feature "Set ENV_MODE=local (activates built-in billing bypass)"
    
    # Set BILLING_ENABLED to false for extra safety
    if grep -q "^BILLING_ENABLED=" "$BACKEND_ENV"; then
        sed -i.bak 's/^BILLING_ENABLED=.*/BILLING_ENABLED=false/' "$BACKEND_ENV"
    else
        echo "BILLING_ENABLED=false" >> "$BACKEND_ENV"
    fi
    feature "Set BILLING_ENABLED=false (additional safety layer)"
    
    # Ensure Redis is available for feature flags (but don't fail if not)
    if ! grep -q "^REDIS_HOST=" "$BACKEND_ENV"; then
        echo "REDIS_HOST=redis" >> "$BACKEND_ENV"
        echo "REDIS_PORT=6379" >> "$BACKEND_ENV"
        info "Added Redis configuration for feature flags"
    fi
    
    success "Backend environment configured for unlimited development mode"
else
    warning "Backend .env not found, creating comprehensive development configuration"
    cat > "$BACKEND_ENV" << 'BACKEND_ENV_CONTENT'
# Development Mode Configuration - Comprehensive Billing Disable
ENV_MODE=local
BILLING_ENABLED=false

# Redis for feature flags
REDIS_HOST=redis
REDIS_PORT=6379

# Basic required environment variables for development
# Replace these with your actual Supabase project credentials
SUPABASE_URL=your_supabase_url_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here
NEXT_PUBLIC_URL=http://localhost:3000
BACKEND_ENV_CONTENT
    success "Created comprehensive backend development configuration"
fi

# =============================================================================
# STEP 2: FRONTEND CONFIGURATION - Local Development Mode UI
# =============================================================================

section "Frontend Configuration - Local Development Mode UI"

FRONTEND_ENV="frontend/.env.local"
if [ -f "$FRONTEND_ENV" ]; then
    cp "$FRONTEND_ENV" "$BACKUP_DIR/.env.local.backup"
    success "Backed up frontend .env.local"
else
    touch "$FRONTEND_ENV"
fi

# Set frontend to local development mode (this disables billing UI throughout)
if grep -q "^NEXT_PUBLIC_ENV_MODE=" "$FRONTEND_ENV"; then
    sed -i.bak 's/^NEXT_PUBLIC_ENV_MODE=.*/NEXT_PUBLIC_ENV_MODE=local/' "$FRONTEND_ENV"
else
    echo "NEXT_PUBLIC_ENV_MODE=local" >> "$FRONTEND_ENV"
fi
feature "Set NEXT_PUBLIC_ENV_MODE=local (disables billing UI components)"

# Set billing disabled flag
if grep -q "^NEXT_PUBLIC_BILLING_ENABLED=" "$FRONTEND_ENV"; then
    sed -i.bak 's/^NEXT_PUBLIC_BILLING_ENABLED=.*/NEXT_PUBLIC_BILLING_ENABLED=false/' "$FRONTEND_ENV"
else
    echo "NEXT_PUBLIC_BILLING_ENABLED=false" >> "$FRONTEND_ENV"
fi
feature "Set NEXT_PUBLIC_BILLING_ENABLED=false (explicit billing disable)"

# Ensure backend URL is correct for local development
if grep -q "^NEXT_PUBLIC_BACKEND_URL=" "$FRONTEND_ENV"; then
    sed -i.bak 's|^NEXT_PUBLIC_BACKEND_URL=.*|NEXT_PUBLIC_BACKEND_URL=http://localhost:8000/api|' "$FRONTEND_ENV"
else
    echo "NEXT_PUBLIC_BACKEND_URL=http://localhost:8000/api" >> "$FRONTEND_ENV"
fi
info "Configured backend URL for localhost development"

success "Frontend configured for unlimited development experience"

# =============================================================================
# STEP 3: ENHANCED BILLING HELPER LIBRARY
# =============================================================================

section "Creating Enhanced Development Billing Helper"

BILLING_HELPER_FILE="frontend/src/lib/billing.ts"
mkdir -p "$(dirname "$BILLING_HELPER_FILE")"

if [ -f "$BILLING_HELPER_FILE" ]; then
    cp "$BILLING_HELPER_FILE" "$BACKUP_DIR/billing.ts.backup"
fi

cat > "$BILLING_HELPER_FILE" << 'BILLING_HELPER_CONTENT'
/**
 * Enhanced Billing Helper - Development Mode
 * Provides unlimited access and premium feature simulation for localhost development
 * 
 * This module transforms the development experience to match production premium tiers
 */

// Environment detection
const isDevelopmentMode = (): boolean => {
  return process.env.NEXT_PUBLIC_ENV_MODE === 'local' || 
         process.env.NODE_ENV === 'development';
};

const isBillingDisabled = (): boolean => {
  return process.env.NEXT_PUBLIC_BILLING_ENABLED === 'false' || isDevelopmentMode();
};

// Backend URL configuration
export const getBackendUrl = (): string => {
  return process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000/api';
};

// Core billing status checker - returns unlimited access in development
export function shouldEnforceBilling(): boolean {
  if (isDevelopmentMode()) {
    console.log('🚀 Development Mode: Billing enforcement disabled - unlimited access');
    return false;
  }
  
  return process.env.NEXT_PUBLIC_BILLING_ENABLED === 'true';
}

// Enhanced billing configuration with development mode features
export function getBillingConfig() {
  const devMode = isDevelopmentMode();
  
  return {
    enabled: !devMode && shouldEnforceBilling(),
    backendUrl: getBackendUrl(),
    mode: process.env.NEXT_PUBLIC_ENV_MODE || 'production',
    developmentMode: devMode,
    unlimitedAccess: devMode,
    premiumFeaturesEnabled: devMode,
    customAgentsEnabled: devMode,
    allModelsAvailable: devMode
  };
}

// React hook for billing status with development mode support
export function useBillingEnabled(): boolean {
  const config = getBillingConfig();
  
  if (config.developmentMode) {
    console.log('🔓 Development Mode: All billing restrictions bypassed');
    return false;
  }
  
  return config.enabled;
}

// Premium feature access helper - always true in development
export function hasFeatureAccess(feature: string): boolean {
  if (isDevelopmentMode()) {
    console.log(`✅ Development Mode: ${feature} feature access granted`);
    return true;
  }
  
  // In production, this would check actual subscription
  return shouldEnforceBilling();
}

// Model access helper - all models available in development
export function hasModelAccess(modelName: string): boolean {
  if (isDevelopmentMode()) {
    console.log(`🤖 Development Mode: ${modelName} model access granted`);
    return true;
  }
  
  // Production logic would check subscription tier
  return shouldEnforceBilling();
}

// Usage limits helper - unlimited in development
export function checkUsageLimit(feature: string, currentUsage: number = 0): {
  allowed: boolean;
  limit: number;
  used: number;
  remaining: number;
  unlimited: boolean;
} {
  if (isDevelopmentMode()) {
    return {
      allowed: true,
      limit: Infinity,
      used: currentUsage,
      remaining: Infinity,
      unlimited: true
    };
  }
  
  // Production limits would be calculated here
  return {
    allowed: true,
    limit: 100,
    used: currentUsage,
    remaining: Math.max(0, 100 - currentUsage),
    unlimited: false
  };
}

// Subscription tier simulation for development
export function getSimulatedSubscriptionTier(): {
  tier: string;
  name: string;
  features: string[];
  unlimited: boolean;
} {
  if (isDevelopmentMode()) {
    return {
      tier: 'development',
      name: 'Development Pro (Unlimited)',
      features: [
        'Unlimited AI model access',
        'Custom agents & workflows',
        'Advanced integrations', 
        'Premium templates',
        'Unlimited usage & storage',
        'All premium features enabled'
      ],
      unlimited: true
    };
  }
  
  return {
    tier: 'free',
    name: 'Free Tier',
    features: ['Basic features'],
    unlimited: false
  };
}

// Development mode status messages
export function getBillingStatusMessage(): string {
  if (isDevelopmentMode()) {
    return '🚀 Running in Development Mode - All premium features enabled, unlimited usage';
  }
  
  return shouldEnforceBilling() ? 'Billing is active' : 'Billing is disabled';
}

// Custom agents availability - always true in development
export function areCustomAgentsEnabled(): boolean {
  if (isDevelopmentMode()) {
    console.log('🤖 Development Mode: Custom agents fully enabled');
    return true;
  }
  
  return shouldEnforceBilling();
}

// Advanced workflows availability - always true in development  
export function areWorkflowsEnabled(): boolean {
  if (isDevelopmentMode()) {
    console.log('⚡ Development Mode: Advanced workflows fully enabled');
    return true;
  }
  
  return shouldEnforceBilling();
}

// Conditional rendering helper for development
export function withBilling<T>(component: T, fallback?: T): T | undefined {
  if (isDevelopmentMode()) {
    return fallback; // In dev mode, show the non-billing version
  }
  
  return shouldEnforceBilling() ? component : fallback;
}

// Development mode feature detector
export { isDevelopmentMode, isBillingDisabled };

// Legacy support
export const isLocalMode = isDevelopmentMode;
BILLING_HELPER_CONTENT

success "Created enhanced billing helper with unlimited development features"

# =============================================================================
# STEP 4: DEVELOPMENT MODE INDICATOR COMPONENT
# =============================================================================

section "Creating Development Mode Indicator"

DEV_INDICATOR_FILE="frontend/src/components/dev-mode-indicator.tsx"
mkdir -p "$(dirname "$DEV_INDICATOR_FILE")"

cat > "$DEV_INDICATOR_FILE" << 'DEV_INDICATOR_CONTENT'
'use client';

import React from 'react';
import { isDevelopmentMode, getBillingStatusMessage } from '@/lib/billing';

export function DevModeIndicator() {
  if (!isDevelopmentMode()) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-gradient-to-r from-green-500 to-blue-500 text-white px-4 py-2 rounded-lg shadow-lg border border-green-400">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
          <span className="text-sm font-medium">
            🚀 Dev Mode: Unlimited Access
          </span>
        </div>
        <div className="text-xs opacity-90 mt-1">
          All premium features enabled
        </div>
      </div>
    </div>
  );
}

export default DevModeIndicator;
DEV_INDICATOR_CONTENT

success "Created development mode indicator component"

# =============================================================================
# STEP 5: ADDITIONAL SAFETY PATCHES (Edge Cases)
# =============================================================================

section "Applying Additional Safety Patches"

# Note: Additional safety patches are not needed as the current codebase
# already has comprehensive ENV_MODE=local checks built into the billing system
# in backend/services/billing.py and backend/utils/config.py
info "✅ Current codebase already has comprehensive ENV_MODE=local billing bypass"
info "   No additional API patches needed - billing system is already development-ready"

# =============================================================================
# STEP 6: FEATURE FLAGS ACTIVATION
# =============================================================================

section "Activating Development Feature Flags"

# Check if Redis is running and activate feature flags
info "Checking Redis availability for feature flags..."

if docker ps --format '{{.Names}}' | grep -q redis; then
    success "Redis container is running - activating feature flags"
    
    # Enable key development feature flags
    FEATURE_FLAGS=(
        "custom_agents:Enable custom agent creation and management"
        "mcp_module:Enable MCP (Model Context Protocol) module"
        "templates_api:Enable templates API for advanced workflows"
        "triggers_api:Enable triggers API for automation"
        "workflows_api:Enable advanced workflows API"
        "knowledge_base:Enable knowledge base functionality"
        "pipedream:Enable Pipedream integration"
        "credentials_api:Enable secure credentials management"
        "suna_default_agent:Enable Suna default agent"
        "advanced_models:Enable access to all premium AI models"
        "unlimited_usage:Remove usage limits and restrictions"
    )
    
    REDIS_CONTAINER=$(docker ps --format '{{.Names}}' | grep redis | head -1)
    
    for flag_desc in "${FEATURE_FLAGS[@]}"; do
        flag_name="${flag_desc%%:*}"
        description="${flag_desc#*:}"
        timestamp=$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)
        
        docker exec "$REDIS_CONTAINER" redis-cli HSET "feature_flag:$flag_name" enabled "true" description "$description" updated_at "$timestamp" > /dev/null
        docker exec "$REDIS_CONTAINER" redis-cli SADD "feature_flags:list" "$flag_name" > /dev/null
        
        feature "Enabled: $flag_name"
    done
    
    success "✨ Activated all development feature flags"
else
    warning "Redis container not running - feature flags will be activated when you start the services"
    info "Feature flags will auto-activate when you run: docker compose up"
fi

# =============================================================================
# STEP 7: COMPREHENSIVE ROLLBACK SCRIPT CREATION
# =============================================================================

section "Creating Comprehensive Rollback System"

ROLLBACK_SCRIPT="rollback_complete_billing_disable.sh"
cat > "$ROLLBACK_SCRIPT" << ROLLBACK_CONTENT
#!/bin/bash

# Comprehensive Rollback Script - Complete Billing Disable
# Generated by disable_billing_complete.sh on $(date)

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

info() { echo -e "\${BLUE}ℹ️  \$1\${NC}"; }
success() { echo -e "\${GREEN}✅ \$1\${NC}"; }
warning() { echo -e "\${YELLOW}⚠️  \$1\${NC}"; }

echo -e "\${PURPLE}╔══════════════════════════════════════════════════════════════╗\${NC}"
echo -e "\${PURPLE}║                                                              ║\${NC}"
echo -e "\${PURPLE}║               🔄 COMPREHENSIVE ROLLBACK 🔄                  ║\${NC}"
echo -e "\${PURPLE}║                                                              ║\${NC}"
echo -e "\${PURPLE}║         Restoring original billing configuration            ║\${NC}"
echo -e "\${PURPLE}║                                                              ║\${NC}"
echo -e "\${PURPLE}╚══════════════════════════════════════════════════════════════╝\${NC}"
echo ""

info "🔄 Rolling back comprehensive billing disable changes..."

# Restore backed up files
if [ -d "$BACKUP_DIR" ]; then
    echo "📁 Restoring files from backup: $BACKUP_DIR"
    
    [ -f "$BACKUP_DIR/.env.backend.backup" ] && cp "$BACKUP_DIR/.env.backend.backup" "backend/.env" && success "✅ Restored backend .env"
    [ -f "$BACKUP_DIR/.env.local.backup" ] && cp "$BACKUP_DIR/.env.local.backup" "frontend/.env.local" && success "✅ Restored frontend .env.local"
    [ -f "$BACKUP_DIR/billing.ts.backup" ] && cp "$BACKUP_DIR/billing.ts.backup" "frontend/src/lib/billing.ts" && success "✅ Restored billing helper"
    
    echo "🗑️  Cleaning up backup directory: $BACKUP_DIR"
    rm -rf "$BACKUP_DIR"
else
    warning "⚠️  Backup directory $BACKUP_DIR not found"
fi

# Remove created development files
[ -f "frontend/src/lib/billing.ts" ] && [ ! -f "$BACKUP_DIR/billing.ts.backup" ] && rm -f "frontend/src/lib/billing.ts" && success "🗑️  Removed development billing helper"
[ -f "frontend/src/components/dev-mode-indicator.tsx" ] && rm -f "frontend/src/components/dev-mode-indicator.tsx" && success "🗑️  Removed dev mode indicator"

# Clean up .bak files created by sed
find . -name "*.bak" -delete 2>/dev/null && success "🧹 Cleaned up .bak files"

success "✅ Rollback completed successfully!"
echo ""
info "📋 What was restored:"
info "   • Backend environment variables (ENV_MODE, BILLING_ENABLED)"
info "   • Frontend environment variables (NEXT_PUBLIC_ENV_MODE, NEXT_PUBLIC_BILLING_ENABLED)"
info "   • Original billing helper library"
echo ""
warning "⚠️  Note: Feature flags in Redis are NOT automatically rolled back"
warning "    Use: ./manage_feature_flags.sh disable-all to disable feature flags"
echo ""
info "🚀 Next steps after rollback:"
info "   1. Restart Docker containers: docker compose down && docker compose up --build"
info "   2. Verify billing is re-enabled in the application"
info "   3. Check that premium features now require subscription"

# Self-destruct
rm -f "\$0"
ROLLBACK_CONTENT

chmod +x "$ROLLBACK_SCRIPT"
success "Created comprehensive rollback script: $ROLLBACK_SCRIPT"

# =============================================================================
# STEP 8: CLEANUP AND FINAL SUMMARY
# =============================================================================

# Clean up .bak files created by sed
find . -name "*.bak" -delete 2>/dev/null || true

section "🎉 COMPREHENSIVE BILLING DISABLE COMPLETED!"

echo ""
echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${GREEN}║                                                              ║${NC}"
echo -e "${GREEN}║                    🚀 SUCCESS! 🚀                           ║${NC}"
echo -e "${GREEN}║                                                              ║${NC}"
echo -e "${GREEN}║      Localhost transformed to production-like experience    ║${NC}"
echo -e "${GREEN}║                                                              ║${NC}"
echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""

success "✨ Billing system completely disabled and premium features activated!"
info "📁 All changes backed up in: $BACKUP_DIR"
info "🔄 Rollback script available: $ROLLBACK_SCRIPT"

echo ""
echo -e "${CYAN}📋 WHAT'S NOW ENABLED:${NC}"
echo -e "${GREEN}   ✅ Unlimited AI model access (GPT-4, Claude, Gemini, etc.)${NC}"
echo -e "${GREEN}   ✅ Custom agents creation and management${NC}"  
echo -e "${GREEN}   ✅ Advanced workflows and automation${NC}"
echo -e "${GREEN}   ✅ Premium integrations and templates${NC}"
echo -e "${GREEN}   ✅ Unlimited usage - no restrictions${NC}"
echo -e "${GREEN}   ✅ All premium features unlocked${NC}"
echo -e "${GREEN}   ✅ Feature flags system activated${NC}"

echo ""
echo -e "${PURPLE}🚀 NEXT STEPS:${NC}"
echo "   1. Restart containers: ${YELLOW}docker compose down && docker compose up --build${NC}"
echo "   2. Visit http://localhost:3000 - you should see 'Dev Mode: Unlimited Access'"
echo "   3. Try creating custom agents - should work without restrictions"
echo "   4. Test premium models - all should be available"
echo "   5. Check feature flags: ${YELLOW}./manage_feature_flags.sh list${NC}"

echo ""
echo -e "${BLUE}🔧 MANAGEMENT COMMANDS:${NC}"
echo "   • View feature flags: ${YELLOW}./manage_feature_flags.sh list${NC}"
echo "   • Test API connectivity: ${YELLOW}./manage_feature_flags.sh test${NC}"
echo "   • Rollback everything: ${YELLOW}./$ROLLBACK_SCRIPT${NC}"

echo ""
warning "🔐 IMPORTANT REMINDERS:"
warning "   • This is for DEVELOPMENT ONLY - never use in production"
warning "   • All premium features are now unlimited on localhost"
warning "   • Use rollback script to restore original billing when needed"

echo ""
success "🎉 Ready for unlimited development! Enjoy your production-like localhost experience!"

echo ""
echo -e "${PURPLE}═══════════════════════════════════════════════════════════════${NC}"
