<html lang="en"><head><style>body {transition: opacity ease-in 0.2s; } 
body[unresolved] {opacity: 0; display: block; overflow: hidden; position: relative; } 
</style><style>body {transition: opacity ease-in 0.2s; } 
body[unresolved] {opacity: 0; display: block; overflow: hidden; position: relative; } 
</style><style>body {transition: opacity ease-in 0.2s; } 
body[unresolved] {opacity: 0; display: block; overflow: hidden; position: relative; } 
</style><style>body {transition: opacity ease-in 0.2s; } 
body[unresolved] {opacity: 0; display: block; overflow: hidden; position: relative; } 
</style><style>body {transition: opacity ease-in 0.2s; } 
body[unresolved] {opacity: 0; display: block; overflow: hidden; position: relative; } 
</style>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Elon Musk: Visionary Entrepreneur - Slide 2</title>
<link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet"/>
<script src="https://d3js.org/d3.v7.min.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
<style>
        /* Base styling and 1920x1080 slide container */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', Arial, sans-serif;
            background: #000000;
            color: #333333;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            overflow: hidden;
        }
        
        .slide-container {
            /* CRITICAL: Standard presentation dimensions */
            width: 1920px;
            height: 1080px;
            max-width: 100vw;
            max-height: 100vh;
            position: relative;
            background: #FFFFFF;
            color: #333333;
            overflow: hidden;
            
            /* Auto-scale to fit viewport while maintaining aspect ratio */
            transform-origin: center center;
            transform: scale(min(100vw / 1920px, 100vh / 1080px));
        }
        
        /* Slide number indicator */
        .slide-number {
            position: absolute;
            bottom: 30px;
            right: 30px;
            font-size: 18px;
            color: #666666;
            font-weight: 500;
            z-index: 1000;
        }
        
        /* Common presentation elements */
        .slide-title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #005A9C;
        }
        
        .slide-subtitle {
            font-size: 32px;
            margin-bottom: 40px;
            color: #333333;
        }
        
        .slide-content {
            font-size: 24px;
            line-height: 1.6;
            color: #333333;
        }
        
        .accent-bar {
            width: 100px;
            height: 4px;
            background-color: #FF6B00;
            margin: 20px 0;
        }
        
        /* Responsive images */
        img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
        }
        
        /* List styling */
        ul, ol {
            margin: 20px 0;
            padding-left: 30px;
        }
        
        li {
            margin: 10px 0;
            font-size: 20px;
            line-height: 1.5;
        }
    </style>

</head>
<body>
<div class="slide-container">
<div style="display: flex; height: 100%; padding: 0;">
<div style="width: 60%; padding: 80px; display: flex; flex-direction: column; justify-content: center;">
<h1 style="font-size: 48px; font-weight: bold; color: rgb(0, 90, 156); margin-bottom: 20px; position: relative; animation: 0.6s ease-out 0s 1 normal none running flash;">Early Life &amp;  ! :)</h1>
<div style="width: 100px; height: 4px; background: #FF6B00; margin-bottom: 40px;"></div>
<div style="font-size: 22px; line-height: 1.8;">
<div style="margin-bottom: 25px; display: flex; align-items: center;">
<i class="fas fa-calendar-alt" style="color: #FF6B00; font-size: 20px; margin-right: 15px;"></i>
<div><strong>Born:</strong><span style="position: relative;">June 28, 1971 in Pretoria, Sou 123123 1th Africa</span></div>
</div>
<div style="margin-bottom: 25px; display: flex; align-items: center;">
<i class="fas fa-graduation-cap" style="color: #FF6B00; font-size: 20px; margin-right: 15px;"></i>
<div style="position: relative;">Education:University of (Physics &amp; Econ !"!! omics)</div>
</div>
<div style="margin-bottom: 25px; display: flex; align-items: center;">
<i class="fas fa-laptop-code" style="color: #FF6B00; font-size: 20px; margin-right: 15px;"></i>
<div><strong>Early Interest:</strong><span>Programming since age 12</span></div>
</div>
<div style="margin-bottom: 25px; display: flex; align-items: center;">
<i class="fas fa-plane" style="color: #FF6B00; font-size: 20px; margin-right: 15px;"></i>
<div><strong>Immigration:</strong><span>Moved to Canada, then USA</span></div>
</div>
</div>
</div>
<div style="width: 40%; background: #f8f9fa; display: flex; align-items: center; justify-content: center; padding: 40px;">
<div style="text-align: center;">
<h3 style="font-size: 28px; color: #005A9C; margin-bottom: 20px;">From ITALYYYY</h3>
<p style="font-size: 18px; color: #666; line-height: 1.6;">Germani</p>
</div>
</div>
</div>
<div class="slide-number">2</div>
</div>

<readwise-tooltip-container></readwise-tooltip-container></body></html>