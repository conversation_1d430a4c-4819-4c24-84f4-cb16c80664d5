## Copy this file to .env and fill values.
## Required keys are marked REQUIRED. Others are optional.

# Environment Mode
# Valid values: local, staging, production
ENV_MODE=local

##### DATABASE (REQUIRED)
SUPABASE_URL=
SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

##### REDIS
# Use "redis" when using docker compose, or "localhost" for fully local
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
# Set false for local/docker compose
REDIS_SSL=false

##### LLM PROVIDERS (At least one is functionally REQUIRED)
# Provide at least one of the following:
ANTHROPIC_API_KEY=
OPENAI_API_KEY=
GROQ_API_KEY=
OPENROUTER_API_KEY=
GEMINI_API_KEY=
XAI_API_KEY=

# AWS Bedrock (only if using Bedrock)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION_NAME=

# OpenAI-compatible
OPENAI_COMPATIBLE_API_KEY=
OPENAI_COMPATIBLE_API_BASE=

##### DATA / SEARCH (REQUIRED)
RAPID_API_KEY=
TAVILY_API_KEY=

##### WEB SCRAPE (REQUIRED)
FIRECRAWL_API_KEY=
# Default used if empty: https://api.firecrawl.dev
FIRECRAWL_URL=

##### AGENT SANDBOX (REQUIRED to use Daytona sandbox)
DAYTONA_API_KEY=
DAYTONA_SERVER_URL=https://app.daytona.io/api
DAYTONA_TARGET=us

##### SECURITY & WEBHOOKS (Recommended)
MCP_CREDENTIAL_ENCRYPTION_KEY=
WEBHOOK_BASE_URL=http://localhost:8000
TRIGGER_WEBHOOK_SECRET=

##### OBSERVABILITY (Optional)
LANGFUSE_PUBLIC_KEY=
LANGFUSE_SECRET_KEY=
LANGFUSE_HOST=https://cloud.langfuse.com

##### BILLING (Optional;)
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=
STRIPE_DEFAULT_PLAN_ID=
STRIPE_DEFAULT_TRIAL_DAYS=14

##### ADMIN
KORTIX_ADMIN_API_KEY=

##### INTEGRATIONS
COMPOSIO_API_KEY=
COMPOSIO_WEBHOOK_SECRET=
COMPOSIO_API_BASE=https://backend.composio.dev

# Pipedream (optional; for MCP)
PIPEDREAM_PROJECT_ID=
PIPEDREAM_CLIENT_ID=
PIPEDREAM_CLIENT_SECRET=
PIPEDREAM_X_PD_ENVIRONMENT=development
