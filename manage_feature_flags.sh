#!/bin/bash

# Feature Flags Management Script for Suna Development
# This script helps manage feature flags via Redis for development purposes

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Helper functions for colored output
info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
success() { echo -e "${GREEN}✅ $1${NC}"; }
warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
error() { echo -e "${RED}❌ $1${NC}"; }
section() { echo -e "\n${PURPLE}🚩 $1${NC}"; }
flag_info() { echo -e "${CYAN}🔧 $1${NC}"; }

# Check if Redis container is running
check_redis() {
    if ! docker ps --format '{{.Names}}' | grep -q redis; then
        error "Redis container is not running!"
        info "Please start Redis with: docker compose up redis -d"
        exit 1
    fi
    success "Redis container is running"
}

# Enable a feature flag via Redis
enable_flag() {
    local flag_name="$1"
    local description="$2"
    local timestamp=$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)
    
    docker exec "$(docker ps --format '{{.Names}}' | grep redis)" redis-cli \
        HSET "feature_flag:$flag_name" \
        enabled "true" \
        description "$description" \
        updated_at "$timestamp" > /dev/null
    
    docker exec "$(docker ps --format '{{.Names}}' | grep redis)" redis-cli \
        SADD "feature_flags:list" "$flag_name" > /dev/null
    
    success "Enabled flag: $flag_name"
    [ -n "$description" ] && flag_info "  Description: $description"
}

# Disable a feature flag via Redis
disable_flag() {
    local flag_name="$1"
    local description="$2"
    local timestamp=$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)
    
    docker exec "$(docker ps --format '{{.Names}}' | grep redis)" redis-cli \
        HSET "feature_flag:$flag_name" \
        enabled "false" \
        description "$description" \
        updated_at "$timestamp" > /dev/null
    
    # Flag stays in the list even when disabled
    success "Disabled flag: $flag_name"
    [ -n "$description" ] && flag_info "  Description: $description"
}

# Delete a feature flag completely
delete_flag() {
    local flag_name="$1"
    
    docker exec "$(docker ps --format '{{.Names}}' | grep redis)" redis-cli \
        DEL "feature_flag:$flag_name" > /dev/null
    
    docker exec "$(docker ps --format '{{.Names}}' | grep redis)" redis-cli \
        SREM "feature_flags:list" "$flag_name" > /dev/null
    
    success "Deleted flag: $flag_name"
}

# Get status of a feature flag
get_flag_status() {
    local flag_name="$1"
    
    local enabled=$(docker exec "$(docker ps --format '{{.Names}}' | grep redis)" redis-cli \
        HGET "feature_flag:$flag_name" enabled 2>/dev/null)
    
    if [ -z "$enabled" ]; then
        warning "Flag '$flag_name' not found"
        return 1
    fi
    
    local description=$(docker exec "$(docker ps --format '{{.Names}}' | grep redis)" redis-cli \
        HGET "feature_flag:$flag_name" description 2>/dev/null)
    
    local updated_at=$(docker exec "$(docker ps --format '{{.Names}}' | grep redis)" redis-cli \
        HGET "feature_flag:$flag_name" updated_at 2>/dev/null)
    
    if [ "$enabled" = "true" ]; then
        success "Flag '$flag_name': ENABLED"
    else
        warning "Flag '$flag_name': DISABLED"
    fi
    
    [ -n "$description" ] && flag_info "  Description: $description"
    [ -n "$updated_at" ] && flag_info "  Updated: $updated_at"
}

# List all feature flags
list_all_flags() {
    local flags=$(docker exec "$(docker ps --format '{{.Names}}' | grep redis)" redis-cli \
        SMEMBERS "feature_flags:list" 2>/dev/null)
    
    if [ -z "$flags" ]; then
        warning "No feature flags found"
        return 0
    fi
    
    echo -e "\n${PURPLE}📋 Feature Flags Status${NC}"
    echo "─────────────────────────────────────────"
    
    for flag in $flags; do
        get_flag_status "$flag"
        echo ""
    done
}

# Test API connectivity
test_api() {
    info "Testing feature flags API connectivity..."
    
    if curl -s "http://localhost:8000/api/feature-flags" > /dev/null; then
        success "✅ API is accessible at http://localhost:8000/api/feature-flags"
        
        # Test a specific flag
        local test_response=$(curl -s "http://localhost:8000/api/feature-flags/custom_agents")
        if echo "$test_response" | grep -q "flag_name"; then
            success "✅ Feature flags API is working correctly"
            flag_info "Sample response: $(echo "$test_response" | head -c 100)..."
        else
            warning "⚠️  API responded but format might be unexpected"
        fi
    else
        error "❌ Cannot reach feature flags API"
        info "Make sure the backend is running: docker compose up backend -d"
    fi
}

# Enable all development flags
enable_dev_flags() {
    section "Enabling development feature flags"
    
    local dev_flags=(
        "custom_agents:Enable custom agent creation and management"
        "mcp_module:Enable MCP (Model Context Protocol) module"
        "templates_api:Enable templates API"
        "triggers_api:Enable triggers API" 
        "agent_triggers:Enable agent triggers functionality"
        "workflows_api:Enable workflows API"
        "knowledge_base:Enable knowledge base functionality"
        "pipedream:Enable Pipedream integration"
        "credentials_api:Enable credentials API"
        "suna_default_agent:Enable Suna default agent"
    )
    
    local enabled_count=0
    
    for flag_desc in "${dev_flags[@]}"; do
        local flag_name="${flag_desc%%:*}"
        local description="${flag_desc#*:}"
        
        enable_flag "$flag_name" "$description"
        ((enabled_count++))
    done
    
    success "✨ Enabled $enabled_count development feature flags"
    info "Use 'list' command to see all flags status"
}

# Disable all feature flags
disable_all_flags() {
    section "Disabling all feature flags"
    
    local flags=$(docker exec "$(docker ps --format '{{.Names}}' | grep redis)" redis-cli \
        SMEMBERS "feature_flags:list" 2>/dev/null)
    
    if [ -z "$flags" ]; then
        warning "No feature flags to disable"
        return 0
    fi
    
    local disabled_count=0
    
    for flag in $flags; do
        disable_flag "$flag" "Disabled by batch operation"
        ((disabled_count++))
    done
    
    success "✨ Disabled $disabled_count feature flags"
}

# Show usage information
show_usage() {
    echo -e "${PURPLE}🚩 Feature Flags Management Script${NC}"
    echo ""
    echo -e "${CYAN}Usage:${NC}"
    echo "  $0 <command> [arguments]"
    echo ""
    echo -e "${CYAN}Commands:${NC}"
    echo "  enable <flag_name> [description]   Enable a specific feature flag"
    echo "  disable <flag_name> [description]  Disable a specific feature flag"
    echo "  delete <flag_name>                 Delete a feature flag completely"
    echo "  status <flag_name>                 Get status of a specific flag"
    echo "  list                               List all feature flags"
    echo "  enable-dev                         Enable all development flags"
    echo "  disable-all                        Disable all feature flags"
    echo "  test                               Test API connectivity"
    echo "  help                               Show this help message"
    echo ""
    echo -e "${CYAN}Examples:${NC}"
    echo "  $0 enable custom_agents 'Enable custom agent functionality'"
    echo "  $0 disable billing_checks"
    echo "  $0 status custom_agents"
    echo "  $0 list"
    echo "  $0 enable-dev"
    echo "  $0 test"
    echo ""
    echo -e "${YELLOW}Note:${NC} This script requires Redis to be running in Docker."
    echo "Start Redis with: docker compose up redis -d"
}

# Main script logic
main() {
    if [ $# -eq 0 ]; then
        show_usage
        exit 1
    fi
    
    local command="$1"
    
    case "$command" in
        "enable")
            if [ $# -lt 2 ]; then
                error "Flag name is required for enable command"
                echo "Usage: $0 enable <flag_name> [description]"
                exit 1
            fi
            check_redis
            enable_flag "$2" "$3"
            ;;
        "disable")
            if [ $# -lt 2 ]; then
                error "Flag name is required for disable command"
                echo "Usage: $0 disable <flag_name> [description]"
                exit 1
            fi
            check_redis
            disable_flag "$2" "$3"
            ;;
        "delete")
            if [ $# -lt 2 ]; then
                error "Flag name is required for delete command"
                echo "Usage: $0 delete <flag_name>"
                exit 1
            fi
            check_redis
            delete_flag "$2"
            ;;
        "status")
            if [ $# -lt 2 ]; then
                error "Flag name is required for status command"
                echo "Usage: $0 status <flag_name>"
                exit 1
            fi
            check_redis
            get_flag_status "$2"
            ;;
        "list")
            check_redis
            list_all_flags
            ;;
        "enable-dev")
            check_redis
            enable_dev_flags
            ;;
        "disable-all")
            check_redis
            disable_all_flags
            ;;
        "test")
            test_api
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            error "Unknown command: $command"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Run the main function with all arguments
main "$@"
