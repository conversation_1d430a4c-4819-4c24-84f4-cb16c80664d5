#!/bin/bash

# Comprehensive Environment Validation Script for Local Development
# This script validates that all required environment variables are properly set

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Helper functions
info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
success() { echo -e "${GREEN}✅ $1${NC}"; }
warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
error() { echo -e "${RED}❌ $1${NC}"; }
section() { echo -e "\n${PURPLE}🔧 $1${NC}"; }

# Function to check if environment variable is set
check_env_var() {
    local var_name=$1
    local var_value=$2
    local is_required=${3:-true}
    local description=$4

    if [ -z "$var_value" ]; then
        if [ "$is_required" = true ]; then
            error "$var_name is REQUIRED but not set - $description"
            return 1
        else
            warning "$var_name is not set (optional) - $description"
            return 0
        fi
    else
        success "$var_name is set - $description"
        return 0
    fi
}

# Function to validate .env file
validate_env_file() {
    local env_file=$1
    local env_name=$2
    
    section "Validating $env_name Environment File: $env_file"
    
    if [ ! -f "$env_file" ]; then
        error "$env_file does not exist!"
        return 1
    fi
    
    # Source the .env file
    set -a
    source "$env_file"
    set +a
    
    success "Successfully loaded $env_file"
    return 0
}

echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${PURPLE}║                                                              ║${NC}"
echo -e "${PURPLE}║           🔍 LOCAL ENVIRONMENT VALIDATION 🔍                ║${NC}"
echo -e "${PURPLE}║                                                              ║${NC}"
echo -e "${PURPLE}║     Comprehensive check for production-like local setup     ║${NC}"
echo -e "${PURPLE}║                                                              ║${NC}"
echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""

# Validate backend .env
if ! validate_env_file "backend/.env" "Backend"; then
    exit 1
fi

# Validate frontend .env.local
if ! validate_env_file "frontend/.env.local" "Frontend"; then
    exit 1
fi

section "🚀 Core Configuration Validation"

# Core environment mode check
check_env_var "ENV_MODE" "$ENV_MODE" true "Must be 'local' for development mode"
if [ "$ENV_MODE" != "local" ]; then
    error "ENV_MODE must be 'local' for proper billing bypass!"
    exit 1
fi

# Billing configuration
check_env_var "BILLING_ENABLED" "$BILLING_ENABLED" false "Should be 'false' for local development"
check_env_var "NEXT_PUBLIC_ENV_MODE" "$NEXT_PUBLIC_ENV_MODE" true "Frontend environment mode"
check_env_var "NEXT_PUBLIC_BILLING_ENABLED" "$NEXT_PUBLIC_BILLING_ENABLED" false "Frontend billing flag"

section "🗃️ Database & Infrastructure"

# Supabase configuration
check_env_var "SUPABASE_URL" "$SUPABASE_URL" true "Supabase project URL"
check_env_var "SUPABASE_ANON_KEY" "$SUPABASE_ANON_KEY" true "Supabase anonymous key"
check_env_var "SUPABASE_SERVICE_ROLE_KEY" "$SUPABASE_SERVICE_ROLE_KEY" true "Supabase service role key"

# Redis configuration
check_env_var "REDIS_HOST" "$REDIS_HOST" true "Redis host (should be 'redis' for Docker or 'localhost' for local)"
check_env_var "REDIS_PORT" "$REDIS_PORT" true "Redis port (usually 6379)"

# URL configuration
check_env_var "NEXT_PUBLIC_BACKEND_URL" "$NEXT_PUBLIC_BACKEND_URL" true "Backend API URL"
check_env_var "NEXT_PUBLIC_URL" "$NEXT_PUBLIC_URL" true "Frontend URL"
check_env_var "FRONTEND_URL" "$FRONTEND_URL" false "Backend reference to frontend URL"

section "🤖 AI Model Providers"

# LLM API keys (at least one required)
llm_keys_found=false
check_env_var "OPENAI_API_KEY" "$OPENAI_API_KEY" false "OpenAI API access" && [ -n "$OPENAI_API_KEY" ] && llm_keys_found=true
check_env_var "ANTHROPIC_API_KEY" "$ANTHROPIC_API_KEY" false "Anthropic/Claude API access" && [ -n "$ANTHROPIC_API_KEY" ] && llm_keys_found=true
check_env_var "GEMINI_API_KEY" "$GEMINI_API_KEY" false "Google Gemini API access" && [ -n "$GEMINI_API_KEY" ] && llm_keys_found=true
check_env_var "OPENROUTER_API_KEY" "$OPENROUTER_API_KEY" false "OpenRouter API access" && [ -n "$OPENROUTER_API_KEY" ] && llm_keys_found=true
check_env_var "MORPH_API_KEY" "$MORPH_API_KEY" false "Morph API for code editing" && [ -n "$MORPH_API_KEY" ] && llm_keys_found=true

if [ "$llm_keys_found" = false ]; then
    error "At least one LLM provider API key is required!"
    exit 1
else
    success "At least one LLM provider is configured"
fi

section "🔍 Search & Web Capabilities"

# Search APIs
check_env_var "TAVILY_API_KEY" "$TAVILY_API_KEY" true "Web search functionality"
check_env_var "FIRECRAWL_API_KEY" "$FIRECRAWL_API_KEY" true "Web scraping functionality"
check_env_var "FIRECRAWL_URL" "$FIRECRAWL_URL" false "Firecrawl service URL"

section "💰 Billing Configuration (Even for Local)"

# Stripe configuration (required even in local mode due to code dependencies)
check_env_var "STRIPE_SECRET_KEY" "$STRIPE_SECRET_KEY" false "Stripe secret key (dummy for local)"
check_env_var "STRIPE_WEBHOOK_SECRET" "$STRIPE_WEBHOOK_SECRET" false "Stripe webhook secret (dummy for local)"

if [ -z "$STRIPE_SECRET_KEY" ]; then
    warning "STRIPE_SECRET_KEY not set - some billing-related code may fail"
    info "Consider setting dummy values for local development"
fi

section "🏗️ Agent Execution Environment"

# Daytona configuration
check_env_var "DAYTONA_API_KEY" "$DAYTONA_API_KEY" true "Agent execution sandbox"
check_env_var "DAYTONA_SERVER_URL" "$DAYTONA_SERVER_URL" true "Daytona server endpoint"
check_env_var "DAYTONA_TARGET" "$DAYTONA_TARGET" true "Daytona deployment target"

section "🔐 Security & Keys"

# MCP and admin keys
check_env_var "MCP_CREDENTIAL_ENCRYPTION_KEY" "$MCP_CREDENTIAL_ENCRYPTION_KEY" true "MCP credential encryption"
check_env_var "KORTIX_ADMIN_API_KEY" "$KORTIX_ADMIN_API_KEY" true "Admin API access"
check_env_var "API_KEY_SECRET" "$API_KEY_SECRET" false "API key generation secret"

section "🔗 Webhook & Integration Configuration"

# Webhook configuration
check_env_var "WEBHOOK_BASE_URL" "$WEBHOOK_BASE_URL" false "Base URL for webhooks"
check_env_var "TRIGGER_WEBHOOK_SECRET" "$TRIGGER_WEBHOOK_SECRET" false "Trigger webhook validation"

# Composio integration
check_env_var "COMPOSIO_API_KEY" "$COMPOSIO_API_KEY" false "Composio integration platform"
check_env_var "COMPOSIO_API_BASE" "$COMPOSIO_API_BASE" false "Composio API endpoint"

section "📊 Optional Services"

# Optional services
check_env_var "RAPID_API_KEY" "$RAPID_API_KEY" false "RapidAPI integrations"
check_env_var "LANGFUSE_PUBLIC_KEY" "$LANGFUSE_PUBLIC_KEY" false "LangFuse observability"
check_env_var "SENDGRID_API_KEY" "$SENDGRID_API_KEY" false "Email notifications"

section "🎯 Pipedream Integration"
check_env_var "PIPEDREAM_PROJECT_ID" "$PIPEDREAM_PROJECT_ID" false "Pipedream project"
check_env_var "PIPEDREAM_CLIENT_ID" "$PIPEDREAM_CLIENT_ID" false "Pipedream client"
check_env_var "PIPEDREAM_CLIENT_SECRET" "$PIPEDREAM_CLIENT_SECRET" false "Pipedream secret"

section "💬 Slack Integration"
check_env_var "SLACK_CLIENT_ID" "$SLACK_CLIENT_ID" false "Slack integration"
check_env_var "SLACK_CLIENT_SECRET" "$SLACK_CLIENT_SECRET" false "Slack secret"

echo ""
echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${GREEN}║                                                              ║${NC}"
echo -e "${GREEN}║                   ✅ VALIDATION COMPLETE ✅                  ║${NC}"
echo -e "${GREEN}║                                                              ║${NC}"
echo -e "${GREEN}║         Your local environment is properly configured!      ║${NC}"
echo -e "${GREEN}║                                                              ║${NC}"
echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""

section "🚀 Next Steps"
info "1. Start the services: ./start.py"
info "2. Enable feature flags: ./manage_feature_flags.sh enable-dev"
info "3. Test the API: ./manage_feature_flags.sh test"
info "4. Visit http://localhost:3000 to access the application"
echo ""
info "🔧 Management Commands:"
info "   • ./manage_feature_flags.sh list     - View all feature flags"
info "   • ./validate_local_env.sh           - Re-run this validation"
info "   • ./disable_billing_complete.sh     - Re-apply billing disable"
echo ""
success "🎉 Ready for unlimited local development!"